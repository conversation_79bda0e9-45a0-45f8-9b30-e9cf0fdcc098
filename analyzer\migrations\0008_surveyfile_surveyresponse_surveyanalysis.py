# Generated by Django 4.2.7 on 2025-06-15 17:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('analyzer', '0007_chatsession_chatmessage'),
    ]

    operations = [
        migrations.CreateModel(
            name='SurveyFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='survey_files/')),
                ('original_filename', models.CharField(blank=True, max_length=255)),
                ('file_type', models.CharField(choices=[('xlsx', 'Excel (.xlsx)'), ('xls', 'Excel (.xls)')], max_length=10)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('processed', models.BooleanField(default=False)),
                ('survey_date', models.DateField(blank=True, help_text='Date when the survey was conducted', null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='survey_files', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SurveyResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('role', models.CharField(blank=True, max_length=100)),
                ('q1_1_speaking_up', models.IntegerField(blank=True, help_text='Speaking up comfort level (1-5)', null=True)),
                ('q1_2_mistakes_held_against', models.IntegerField(blank=True, help_text='Mistakes held against (1-5)', null=True)),
                ('q1_3_respect_when_not_knowing', models.IntegerField(blank=True, help_text='Respect when not knowing (1-5)', null=True)),
                ('q2_1_workload_manageable', models.IntegerField(blank=True, help_text='Workload manageability (1-5)', null=True)),
                ('q2_2_tools_and_resources', models.IntegerField(blank=True, help_text='Tools and resources adequacy (1-5)', null=True)),
                ('q2_3_work_life_balance', models.IntegerField(blank=True, help_text='Work-life balance (1-5)', null=True)),
                ('q3_1_understanding_clients', models.FloatField(blank=True, help_text='Understanding client needs (1-5)', null=True)),
                ('q3_2_support_handling_clients', models.FloatField(blank=True, help_text='Support in handling clients (1-5)', null=True)),
                ('q3_3_tools_for_client_service', models.FloatField(blank=True, help_text='Tools for client service (1-5)', null=True)),
                ('q4_1_help_responsiveness', models.IntegerField(blank=True, help_text='Help responsiveness (1-5)', null=True)),
                ('q4_2_conflict_resolution', models.IntegerField(blank=True, help_text='Conflict resolution (1-5)', null=True)),
                ('q4_3_sharing_updates', models.IntegerField(blank=True, help_text='Sharing updates (1-5)', null=True)),
                ('open_feedback', models.TextField(blank=True, help_text='Open-ended feedback')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('survey_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='analyzer.surveyfile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SurveyAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_responses', models.IntegerField(default=0)),
                ('valid_responses', models.IntegerField(default=0)),
                ('avg_psychological_safety', models.FloatField(blank=True, null=True)),
                ('avg_work_environment', models.FloatField(blank=True, null=True)),
                ('avg_client_service', models.FloatField(blank=True, null=True)),
                ('avg_team_collaboration', models.FloatField(blank=True, null=True)),
                ('overall_satisfaction', models.FloatField(blank=True, null=True)),
                ('role_distribution', models.JSONField(default=dict)),
                ('question_averages', models.JSONField(default=dict)),
                ('satisfaction_distribution', models.JSONField(default=dict)),
                ('feedback_themes', models.JSONField(default=list)),
                ('feedback_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('survey_file', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analysis', to='analyzer.surveyfile')),
            ],
        ),
    ]
