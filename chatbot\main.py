from langchain_ollama import OllamaLLM
import sys

def create_chat():
    try:
        # Initialize the LLM with specific parameters
        llm = OllamaLLM(
            model="llama3",
            temperature=0.7,
            base_url="http://localhost:11434"
        )
        
        # Test the connection
        result = llm.invoke("Hello, please introduce yourself.")
        print("Model response:", result)
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure Ollama is running and the model is properly installed")
        sys.exit(1)

if __name__ == "__main__":
    create_chat()