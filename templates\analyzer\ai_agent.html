{% extends 'base.html' %}

{% block title %}AI Agent - Vermeg Insights{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        height: 70vh;
        display: flex;
        flex-direction: column;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        background: #fff;
    }

    .chat-header {
        background: linear-gradient(135deg, #e31937, #c41230);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem 0.5rem 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: #f8f9fa;
    }

    .message {
        margin-bottom: 1rem;
        display: flex;
        align-items: flex-start;
    }

    .message.user {
        justify-content: flex-end;
    }

    .message.ai {
        justify-content: flex-start;
    }

    .message-content {
        max-width: 70%;
        padding: 0.75rem 1rem;
        border-radius: 1rem;
        word-wrap: break-word;
    }

    .message.user .message-content {
        background: #e31937;
        color: white;
        border-bottom-right-radius: 0.25rem;
    }

    .message.ai .message-content {
        background: white;
        border: 1px solid #dee2e6;
        border-bottom-left-radius: 0.25rem;
    }

    .chat-input {
        border-top: 1px solid #dee2e6;
        padding: 1rem;
        background: white;
        border-radius: 0 0 0.5rem 0.5rem;
        display: flex;
        gap: 1rem;
    }

    .typing-indicator {
        display: none;
        padding: 0.5rem 1rem;
        font-style: italic;
        color: #6c757d;
    }

    .welcome-message {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }

    .welcome-message .icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #e31937;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="chat-container">
        <div class="chat-header">
            <h5 class="mb-0">AI Business Analyst</h5>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <h4>👋 Welcome to AI Business Analyst</h4>
                <p>I can help you analyze client data, identify trends, and provide actionable insights.</p>
                <p>Try asking me about:</p>
                <ul class="list-unstyled">
                    <li>📊 Client performance metrics</li>
                    <li>📈 Business trends and patterns</li>
                    <li>📧 Professional email drafting</li>
                    <li>💡 Strategic recommendations</li>
                </ul>
            </div>
            
            <div class="typing-indicator" style="display: none;">
                <span>AI is thinking</span>
                <span class="typing-dots">...</span>
            </div>
        </div>
        
        <div class="chat-input">
            {% csrf_token %}
            <textarea id="messageInput" 
                      class="form-control" 
                      placeholder="Type your message... (Press Enter to send)" 
                      rows="1"
                      onkeypress="handleKeyPress(event)"></textarea>
            <button class="btn btn-primary" onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>
</div>

<!-- Include marked.js for markdown parsing -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

{% block extra_js %}
<script>
let currentSessionId = null;

function getCsrfToken() {
    const token = document.querySelector('meta[name="csrf-token"]')?.content ||
                 document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (!token) throw new Error('CSRF token not found');
    return token;
}

function showTypingIndicator() {
    const indicator = document.querySelector('.typing-indicator');
    if (indicator) indicator.style.display = 'block';
}

function hideTypingIndicator() {
    const indicator = document.querySelector('.typing-indicator');
    if (indicator) indicator.style.display = 'none';
}

function hideWelcomeMessage() {
    const welcome = document.querySelector('.welcome-message');
    if (welcome) welcome.style.display = 'none';
}

function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addMessageToChat(message, isUser = false) {
    hideWelcomeMessage();
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user' : 'ai'}`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    if (isUser) {
        contentDiv.textContent = message;
    } else {
        contentDiv.innerHTML = marked.parse(message);
    }
    
    messageDiv.appendChild(contentDiv);
    document.getElementById('chatMessages').appendChild(messageDiv);
    scrollToBottom();
}

async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    try {
        messageInput.disabled = true;
        addMessageToChat(message, true);
        showTypingIndicator();
        
        const response = await fetch('/api/chat/message/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                message: message,
                session_id: currentSessionId
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        currentSessionId = data.session_id;
        addMessageToChat(data.response);
        
        messageInput.value = '';
        
    } catch (error) {
        console.error('Error:', error);
        addMessageToChat('⚠️ ' + error.message);
    } finally {
        messageInput.disabled = false;
        hideTypingIndicator();
        messageInput.focus();
    }
}

function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Initialize the chat interface
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('messageInput');
    if (messageInput) messageInput.focus();
});
</script>
{% endblock %}
{% endblock %}